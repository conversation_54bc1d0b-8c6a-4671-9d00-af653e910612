const fs = require('fs');
const http = require('http');

// --- 配置 ---
const PLAYLIST_ID = '7091014698';
const CLOUD_DATA_PATH = './网易云云盘信息57W以及切割脚本/网易云云盘信息57W.json';
const API_HOST = 'localhost';
const API_PORT = 3000; // 默认的API端口
const OUTPUT_FILE = `playlist_${PLAYLIST_ID}_from_cloud.json`;
// ---

// HTTP GET 请求辅助函数
function httpGet(path) {
    return new Promise((resolve, reject) => {
        const options = { hostname: API_HOST, port: API_PORT, path, method: 'GET' };
        const req = http.request(options, res => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    if (res.statusCode >= 400) {
                        return reject(new Error(`API Error: Status ${res.statusCode}. ${data}`));
                    }
                    resolve(JSON.parse(data));
                } catch (e) {
                    reject(new Error('Failed to parse JSON response. Is the API server running?'));
                }
            });
        });
        req.on('error', error => reject(new Error(`API request failed: ${error.message}. Make sure NeteaseCloudMusicApi-win.exe is running.`)));
        req.end();
    });
}

async function main() {
    try {
        // 1. 加载本地云盘数据并创建ID-Song的映射
        console.log(`[1/4] Loading local cloud music data from ${CLOUD_DATA_PATH}...`);
        if (!fs.existsSync(CLOUD_DATA_PATH)) {
            throw new Error(`Cloud data file not found at: ${CLOUD_DATA_PATH}`);
        }
        const cloudDataFile = fs.readFileSync(CLOUD_DATA_PATH, 'utf-8');
        const cloudData = JSON.parse(cloudDataFile);
        
        const cloudSongsArray = cloudData.data || (Array.isArray(cloudData) ? cloudData : []);
        if (cloudSongsArray.length === 0) {
            throw new Error('Cloud data is empty or not in the expected format { "data": [...] }.');
        }

        const cloudSongsMap = new Map();
        for (const song of cloudSongsArray) {
            const songId = song.id || song.songId;
            if (songId) {
                cloudSongsMap.set(songId, song);
            }
        }
        console.log(`Loaded ${cloudSongsMap.size} unique songs into memory map.`);

        // 2. 从API获取歌单所有歌曲ID
        console.log(`[2/4] Fetching playlist track IDs for playlist: ${PLAYLIST_ID}...`);
        const playlistDetail = await httpGet(`/playlist/detail?id=${PLAYLIST_ID}`);
        if (!playlistDetail.playlist || !playlistDetail.playlist.trackIds) {
            throw new Error('Could not fetch playlist details or trackIds are missing.');
        }
        const trackIds = playlistDetail.playlist.trackIds.map(t => t.id);
        console.log(`Found ${trackIds.length} tracks in the playlist.`);

        // 3. 匹配歌单歌曲和本地云盘数据
        console.log('[3/4] Matching playlist songs with local data...');
        const matchedSongs = [];
        let notFoundCount = 0;
        for (const trackId of trackIds) {
            if (cloudSongsMap.has(trackId)) {
                matchedSongs.push(cloudSongsMap.get(trackId));
            } else {
                notFoundCount++;
            }
        }
        console.log(`Matching complete. Found ${matchedSongs.length} songs. Could not find ${notFoundCount} songs in local data.`);

        // 4. 格式化并保存结果
        console.log(`[4/4] Writing matched songs to ${OUTPUT_FILE}...`);
        const finalOutput = { data: matchedSongs };
        fs.writeFileSync(OUTPUT_FILE, JSON.stringify(finalOutput, null, 2));

        console.log(`\n✅ Success! The dataset has been saved to ${OUTPUT_FILE}`);
        console.log(`\nTo use this script:`);
        console.log(`1. Double-click to run '网易云盘导入/NeteaseCloudMusicApi-win.exe' to start the API server.`);
        console.log(`2. In your terminal, execute: node findPlaylistSongsInCloud.js`);

    } catch (error) {
        console.error('\n❌ An error occurred:');
        console.error(error.message);
    }
}

main();
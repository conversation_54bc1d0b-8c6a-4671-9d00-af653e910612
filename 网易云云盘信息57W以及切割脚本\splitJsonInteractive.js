const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 配置默认值
const maxFileSize = 30 * 1024 * 1024; // 最大文件大小 30MB

// 创建命令行交互接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 主函数
async function main() {
    try {
        // 获取输入文件名
        const inputFile = await askQuestion("请输入 JSON 文件的路径（如 D:\\temp\\网易云云盘信息30W.json）：");
        if (!fs.existsSync(inputFile)) {
            console.error("文件不存在，请检查路径！");
            process.exit(1);
        }

        // 获取输出目录
        const outputDirectory = await askQuestion("请输入输出目录路径（如 D:\\temp\\output\\）：");
        if (!fs.existsSync(outputDirectory)) {
            fs.mkdirSync(outputDirectory, { recursive: true });
            console.log("输出目录已创建：", outputDirectory);
        }

        // 获取分割成多少个文件
        const numFiles = await askQuestion("请输入想要分割成多少个文件：");
        const totalSize = fs.statSync(inputFile).size; // 获取总文件大小
        const perFileSize = Math.ceil(totalSize / numFiles); // 计算每个文件的大小

        console.log(`将文件分割为 ${numFiles} 个文件，每个文件大约 ${Math.round(perFileSize / (1024 * 1024))} MB。`);

        // 开始分割文件
        await splitJsonFile(inputFile, outputDirectory, numFiles, perFileSize);
    } catch (error) {
        console.error("操作失败：", error);
    } finally {
        rl.close();
    }
}

// 分割 JSON 文件
async function splitJsonFile(inputFile, outputDirectory, numFiles, perFileSize) {
    try {
        // 读取 JSON 文件
        const rawData = fs.readFileSync(inputFile, 'utf-8');
        const jsonObject = JSON.parse(rawData);

        // 检查格式
        if (!jsonObject.data || !Array.isArray(jsonObject.data)) {
            console.error("输入的 JSON 文件格式不正确，缺少 'data' 数组！");
            return;
        }

        const dataList = jsonObject.data;
        let currentSize = 0; // 当前文件大小
        let chunkIndex = 1;  // 文件编号
        let currentChunk = []; // 当前分割的 JSON 数据

        // 遍历数据
        for (const item of dataList) {
            const itemSize = Buffer.byteLength(JSON.stringify(item)); // 计算当前元素的大小
            currentChunk.push(item);
            currentSize += itemSize;

            // 如果当前文件大小超过每个文件的大小限制，则写入文件
            if (currentSize > perFileSize) {
                await writeToFile(currentChunk, outputDirectory, chunkIndex);
                chunkIndex++;
                currentChunk = [];
                currentSize = 0;
            }

            // 如果已经写入了指定的文件数，停止
            if (chunkIndex > numFiles) {
                break;
            }
        }

        // 写入剩余的数据块
        if (currentChunk.length > 0 && chunkIndex <= numFiles) {
            await writeToFile(currentChunk, outputDirectory, chunkIndex);
        }

        console.log("文件分割完成。");
    } catch (error) {
        console.error("分割文件时出错：", error);
    }
}

// 写入文件函数
async function writeToFile(data, outputDirectory, chunkIndex) {
    // 构造新的 JSON 对象
    const outputData = { data: data };
    const outputFilePath = path.join(outputDirectory, `output_${chunkIndex}.json`);

    // 写入 JSON 文件
    fs.writeFileSync(outputFilePath, JSON.stringify(outputData, null, 2), 'utf-8');

    // 获取文件大小
    const stats = fs.statSync(outputFilePath);
    const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2); // 转换为 MB，保留两位小数

    console.log(`已创建文件: output_${chunkIndex}.json (大小: ${fileSizeInMB} MB)`);
}

// 询问问题并返回用户输入
function askQuestion(query) {
    return new Promise((resolve) => rl.question(query, resolve));
}

// 执行主函数
main();
